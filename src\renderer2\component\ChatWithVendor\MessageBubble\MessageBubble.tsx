import React, { useState, useRef } from 'react';
import styles from './MessageBubble.module.scss';
import clsx from 'clsx';

// Helper: extract links from text and split string into parts
const parseLinks = (text: string) => {
  const urlRegex = /(https?:\/\/[^\s<]+)|(www\.[^\s<]+)/gi;
  const elements: (string | { url: string })[] = [];
  let lastIndex = 0;

  let match: RegExpExecArray | null;
  while ((match = urlRegex.exec(text)) !== null) {
    // Push text before the link
    if (match.index > lastIndex) {
      elements.push(text.substring(lastIndex, match.index));
    }
    // Push the link
    elements.push({ url: match[0] });
    lastIndex = match.index + match[0].length;
  }
  // Push any remaining text
  if (lastIndex < text.length) {
    elements.push(text.substring(lastIndex));
  }
  return elements;
};

// Microlink Preview Component
const LinkPreview: React.FC<{ url: string; position: { x: number, y: number } }> = ({ url, position }) => {
  const [data, setData] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  React.useEffect(() => {
    let cancelled = false;
    setLoading(true);
    fetch(`https://api.microlink.io/?url=${encodeURIComponent(url)}`)
      .then(res => res.json())
      .then(res => {
        if (!cancelled) {
          setData(res.data);
          setLoading(false);
        }
      });
    return () => { cancelled = true; };
  }, [url]);

  if (loading) {
    return (
      <div className={styles.linkPreview} style={{ left: position.x, top: position.y }}>
        Loading preview...
      </div>
    );
  }
  if (!data) return null;
  return (
    <div className={styles.linkPreview} style={{ left: position.x, top: position.y }}>
      {data.image && <img src={data.image.url || data.image} alt="" style={{ width: 120, maxHeight: 70, objectFit: 'cover', borderRadius: 6 }} />}
      <div><strong>{data.title}</strong></div>
      <div style={{ fontSize: 12, color: '#888' }}>{data.description}</div>
      <div style={{ fontSize: 11, color: '#aaa' }}>{data.url}</div>
    </div>
  );
};

// Main MessageBubble
interface MessageBubbleProps {
  text: string;
  isMyMessage: boolean;
}

const MessageBubble: React.FC<MessageBubbleProps> = ({ text, isMyMessage }) => {
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [previewPos, setPreviewPos] = useState<{ x: number, y: number }>({ x: 0, y: 0 });

  const handleMouseOver = (e: React.MouseEvent, url: string) => {
    const rect = (e.target as HTMLElement).getBoundingClientRect();
    // Position preview below the link, with some offset
    setPreviewPos({ x: rect.left, y: rect.bottom + 6 });
    setPreviewUrl(url.startsWith('http') ? url : 'http://' + url);
  };
  const handleMouseOut = () => {
    setPreviewUrl(null);
  };

  // Replace links with React <a> tags that show a preview on hover
  const content = parseLinks(text).map((part, i) =>
    typeof part === 'string' ? (
      <React.Fragment key={i}>{part}</React.Fragment>
    ) : (
      <a
        href={part.url.startsWith('http') ? part.url : 'http://' + part.url}
        target="_blank"
        rel="noopener noreferrer"
        key={i}
        onMouseOver={(e) => handleMouseOver(e, part.url)}
        onMouseOut={handleMouseOut}
        //style={{ color: '#53a2ff', textDecoration: 'underline', position: 'relative' }}
      >
        {part.url}
      </a>
    )
  );

  return (
    <div 
      className={clsx(
        styles.messageBubble, 
        !isMyMessage && styles.othersMessage
      )}
      style={{ position: 'relative' }}
    >
      {content}
      {previewUrl && <LinkPreview url={previewUrl} position={previewPos} />}
    </div>
  );
};

export default MessageBubble;
